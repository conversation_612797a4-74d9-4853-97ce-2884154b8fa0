{"name": "marine-traffic", "version": "1.0.0", "main": "index.js", "scripts": {"scrape": "node marine-traffic-scraper.js", "scrape-prod": "node marine-scraper-production.js", "test-scrape": "node test-marine-scraper.js", "scheduler": "node scheduler.js start", "scrape-once": "node scheduler.js once", "clean-data": "node scheduler.js clean", "run-once": "node run-with-config.js once", "run-scheduler": "node run-with-config.js scheduler", "run-clean": "node run-with-config.js clean", "show-config": "node run-with-config.js config", "install-browsers": "npx playwright install"}, "keywords": ["marine-traffic", "scraper", "playwright"], "author": "", "license": "ISC", "description": "Marine Traffic data scraper using Playwright", "dependencies": {"playwright": "^1.54.2"}, "devDependencies": {"@playwright/test": "^1.54.2", "@types/node": "^24.3.0"}}